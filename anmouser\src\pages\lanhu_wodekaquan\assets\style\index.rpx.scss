.page {
  background-color: rgba(247, 247, 247, 1);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  .group_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 750rpx;
    height: 172rpx;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 64rpx;
      .text_1 {
        width: 64rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 24rpx;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 14rpx 0 0 32rpx;
      }
      .thumbnail_1 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 502rpx;
      }
      .thumbnail_2 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 6rpx;
      }
      .thumbnail_3 {
        width: 38rpx;
        height: 38rpx;
        margin: 14rpx 30rpx 0 6rpx;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 108rpx;
      .thumbnail_4 {
        width: 18rpx;
        height: 34rpx;
        margin: 38rpx 0 0 36rpx;
      }
      .text_2 {
        width: 128rpx;
        height: 44rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 32rpx;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 44rpx;
        margin: 32rpx 0 0 22rpx;
      }
      .image_1 {
        width: 174rpx;
        height: 64rpx;
        margin: 22rpx 12rpx 0 360rpx;
      }
    }
  }
  .group_2 {
    position: relative;
    width: 750rpx;
    height: 1454rpx;
    margin-bottom: 2rpx;
    .box_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 88rpx;
      width: 750rpx;
      .text-wrapper_1 {
        width: 590rpx;
        height: 44rpx;
        margin: 22rpx 0 0 80rpx;
        .text_3 {
          width: 90rpx;
          height: 44rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 30rpx;
        }
        .text_4 {
          width: 90rpx;
          height: 44rpx;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 30rpx;
          margin-left: 160rpx;
        }
        .text_5 {
          width: 90rpx;
          height: 44rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 30rpx;
          margin-left: 160rpx;
        }
      }
      .section_1 {
        width: 90rpx;
        height: 6rpx;
        margin: 16rpx 0 0 80rpx;
        .section_2 {
          background-color: rgba(11, 206, 148, 1);
          width: 90rpx;
          height: 6rpx;
        }
      }
    }
    .text-wrapper_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 8px 8px;
      width: 702rpx;
      height: 282rpx;
      margin: 204rpx 0 0 24rpx;
      .text_6 {
        width: 178rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 44rpx 0 0 24rpx;
      }
      .text_7 {
        width: 178rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 12rpx 0 0 24rpx;
      }
      .text_8 {
        width: 262rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 12rpx 0 0 24rpx;
      }
      .text_9 {
        width: 158rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 6rpx 0 0 126rpx;
      }
      .text_10 {
        width: 158rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 6rpx 0 22rpx 122rpx;
      }
    }
    .box_4 {
      width: 702rpx;
      height: 198rpx;
      margin: 20rpx 0 0 24rpx;
      .section_3 {
        width: 186rpx;
        height: 198rpx;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_1 {
          width: 150rpx;
          height: 152rpx;
          margin: 34rpx 0 0 14rpx;
          .text-wrapper_3 {
            width: 150rpx;
            height: 76rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_11 {
              width: 150rpx;
              height: 76rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_12 {
              width: 150rpx;
              height: 76rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 52rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 52rpx;
            }
          }
          .text_13 {
            width: 72rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 40rpx 0 0 44rpx;
          }
        }
      }
      .section_4 {
        background-color: rgba(255, 255, 255, 1);
        height: 198rpx;
        width: 516rpx;
        .box_5 {
          width: 466rpx;
          height: 94rpx;
          margin: 26rpx 0 0 26rpx;
          .text-group_2 {
            width: 184rpx;
            height: 94rpx;
            .text_14 {
              width: 184rpx;
              height: 44rpx;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 30rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 30rpx;
            }
            .text_15 {
              width: 74rpx;
              height: 36rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin-top: 14rpx;
            }
          }
          .text-wrapper_4 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 48rpx;
            margin-top: 22rpx;
            width: 128rpx;
            .text_16 {
              width: 96rpx;
              height: 34rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin: 8rpx 0 0 16rpx;
            }
          }
        }
        .box_6 {
          width: 466rpx;
          height: 36rpx;
          margin: 30rpx 0 12rpx 26rpx;
          .text_17 {
            width: 252rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
          .thumbnail_5 {
            width: 12rpx;
            height: 20rpx;
            margin-top: 16rpx;
          }
        }
      }
    }
    .box_7 {
      width: 702rpx;
      height: 198rpx;
      margin: 20rpx 0 0 24rpx;
      .section_5 {
        width: 186rpx;
        height: 198rpx;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_3 {
          width: 150rpx;
          height: 152rpx;
          margin: 34rpx 0 0 14rpx;
          .text-wrapper_5 {
            width: 150rpx;
            height: 76rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_18 {
              width: 150rpx;
              height: 76rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_19 {
              width: 150rpx;
              height: 76rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 52rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 52rpx;
            }
          }
          .text_20 {
            width: 120rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 40rpx 0 0 18rpx;
          }
        }
      }
      .section_6 {
        height: 198rpx;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 516rpx;
        .box_8 {
          width: 468rpx;
          height: 94rpx;
          margin: 26rpx 0 0 26rpx;
          .text-group_4 {
            width: 260rpx;
            height: 94rpx;
            .text_21 {
              width: 260rpx;
              height: 44rpx;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 30rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 30rpx;
            }
            .text_22 {
              width: 96rpx;
              height: 36rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin-top: 14rpx;
            }
          }
          .section_7 {
            background-color: rgba(153, 153, 153, 1);
            width: 96rpx;
            height: 72rpx;
            margin-top: 10rpx;
          }
        }
        .text-wrapper_6 {
          width: 252rpx;
          height: 36rpx;
          margin: 32rpx 0 10rpx 26rpx;
          .text_23 {
            width: 252rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }
    }
    .box_9 {
      width: 702rpx;
      height: 198rpx;
      margin: 20rpx 0 226rpx 24rpx;
      .box_10 {
        width: 186rpx;
        height: 198rpx;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_5 {
          width: 150rpx;
          height: 152rpx;
          margin: 34rpx 0 0 14rpx;
          .text-wrapper_7 {
            width: 150rpx;
            height: 76rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_24 {
              width: 150rpx;
              height: 76rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 20rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_25 {
              width: 150rpx;
              height: 76rpx;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 52rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 52rpx;
            }
          }
          .text_26 {
            width: 120rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 40rpx 0 0 18rpx;
          }
        }
      }
      .box_11 {
        height: 198rpx;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 516rpx;
        .box_12 {
          width: 470rpx;
          height: 94rpx;
          margin: 26rpx 0 0 26rpx;
          .text-group_6 {
            width: 260rpx;
            height: 94rpx;
            .text_27 {
              width: 260rpx;
              height: 44rpx;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 30rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 30rpx;
            }
            .text_28 {
              width: 96rpx;
              height: 36rpx;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 24rpx;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24rpx;
              margin-top: 14rpx;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(153, 153, 153, 1);
            height: 72rpx;
            margin-top: 14rpx;
            width: 96rpx;
            .text_29 {
              width: 66rpx;
              height: 16rpx;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 22rpx;
              font-family: Source Han Sans CN-Bold;
              font-weight: 700;
              text-align: left;
              white-space: nowrap;
              line-height: 22rpx;
              margin: 20rpx 0 0 18rpx;
            }
          }
        }
        .text-wrapper_9 {
          width: 252rpx;
          height: 36rpx;
          margin: 32rpx 0 10rpx 26rpx;
          .text_30 {
            width: 252rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
          }
        }
      }
    }
    .box_13 {
      position: absolute;
      left: 24rpx;
      top: 110rpx;
      width: 186rpx;
      height: 198rpx;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text-group_7 {
        width: 150rpx;
        height: 152rpx;
        margin: 34rpx 0 0 14rpx;
        .text-wrapper_10 {
          width: 150rpx;
          height: 76rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          .text_31 {
            width: 150rpx;
            height: 76rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 20rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
          }
          .text_32 {
            width: 150rpx;
            height: 76rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 52rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 52rpx;
          }
        }
        .text_33 {
          width: 72rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
          margin: 40rpx 0 0 44rpx;
        }
      }
    }
    .box_14 {
      height: 198rpx;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG5e495abe9cd9704921ffa227bd61dae8.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 516rpx;
      position: absolute;
      left: 210rpx;
      top: 110rpx;
      .block_1 {
        width: 466rpx;
        height: 94rpx;
        margin: 26rpx 0 0 26rpx;
        .text-group_8 {
          width: 184rpx;
          height: 94rpx;
          .text_34 {
            width: 184rpx;
            height: 44rpx;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 30rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
          }
          .text_35 {
            width: 74rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 24rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 14rpx;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 48rpx;
          margin-top: 22rpx;
          width: 128rpx;
          .text_36 {
            width: 96rpx;
            height: 34rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin: 8rpx 0 0 16rpx;
          }
        }
      }
      .block_2 {
        width: 446rpx;
        height: 46rpx;
        margin: 30rpx 0 2rpx 26rpx;
        .text_37 {
          width: 252rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 24rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24rpx;
        }
        .thumbnail_6 {
          width: 12rpx;
          height: 20rpx;
          margin-top: 26rpx;
        }
      }
    }
  }
}
